
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 260 40% 98%;
    --foreground: 260 10% 10%;

    --card: 0 0% 100%;
    --card-foreground: 260 10% 10%;

    --popover: 0 0% 100%;
    --popover-foreground: 260 10% 10%;

    --primary: 260 84% 60%;
    --primary-foreground: 0 0% 100%;

    --secondary: 43 85% 60%;
    --secondary-foreground: 260 10% 10%;

    --muted: 260 10% 95%;
    --muted-foreground: 260 10% 40%;

    --accent: 260 40% 96%;
    --accent-foreground: 260 10% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;

    --border: 260 20% 90%;
    --input: 260 20% 90%;
    --ring: 260 84% 60%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 260 30% 7%;
    --foreground: 0 0% 95%;

    --card: 260 30% 10%;
    --card-foreground: 0 0% 95%;

    --popover: 260 30% 10%;
    --popover-foreground: 0 0% 95%;

    --primary: 260 70% 70%;
    --primary-foreground: 260 10% 10%;

    --secondary: 43 85% 60%;
    --secondary-foreground: 260 10% 10%;

    --muted: 260 20% 20%;
    --muted-foreground: 260 10% 70%;

    --accent: 260 30% 20%;
    --accent-foreground: 0 0% 95%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 95%;

    --border: 260 20% 20%;
    --input: 260 20% 20%;
    --ring: 260 70% 70%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

.card-gradient {
  background: linear-gradient(135deg, #8B5CF6 0%, #6E3AD6 100%);
}

.gold-gradient {
  background: linear-gradient(135deg, #F7DE7E 0%, #EFC044 100%);
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .text-pretty {
    text-wrap: pretty;
  }
}
