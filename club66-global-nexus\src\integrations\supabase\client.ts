// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://tklwdscpbddieykqfbdy.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRrbHdkc2NwYmRkaWV5a3FmYmR5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg0MjI5NTQsImV4cCI6MjA2Mzk5ODk1NH0.gCqlGCYbnFpIG1k9ZEhTyjMdmypiZm7f2ELm_bjLve4";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);