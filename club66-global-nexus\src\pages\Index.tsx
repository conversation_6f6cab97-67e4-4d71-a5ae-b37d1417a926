
import Layout from '@/components/layout/Layout';
// import Hero from '@/components/home/<USER>';  // Temporarily hidden
import HeroSlider from '@/components/home/<USER>';
import MembershipPlans from '@/components/home/<USER>';
import Benefits from '@/components/home/<USER>';
import DigitalCard from '@/components/home/<USER>';
import AffiliateProgram from '@/components/home/<USER>';
import SocialBenefits from '@/components/home/<USER>';
import CTA from '@/components/home/<USER>';

const Index = () => {
  return (
    <Layout>
      {/* <Hero /> */}  {/* Temporarily hidden - can be re-enabled when needed */}
      <HeroSlider />
      <Benefits />
      <MembershipPlans />
      <DigitalCard />
      <AffiliateProgram />
      <SocialBenefits />
      <CTA />
    </Layout>
  );
};

export default Index;
